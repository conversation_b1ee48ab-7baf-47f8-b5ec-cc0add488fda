"""
Localization system for the Telegram bot.
Manages text strings in multiple languages.
"""

from typing import Dict
from enum import Enum


class Language(Enum):
    """Supported languages"""
    ENGLISH = "en"
    RUSSIAN = "ru"


class Localization:
    """Handles localization of text strings"""
    
    def __init__(self, default_language: Language = Language.RUSSIAN):
        self.default_language = default_language
        self._texts = self._load_texts()
    
    def _load_texts(self) -> Dict[str, Dict[str, str]]:
        """Load all text strings organized by key and language"""
        return {
            # Rate limiting messages
            "rate_limit_exceeded": {
                "en": "⚠️ You're sending messages too fast. Please wait a moment.",
                "ru": "⚠️ Вы отправляете сообщения слишком быстро. Пожалуйста, подождите немного."
            },
            
            # Error messages
            "no_response": {
                "en": "🚨 Sorry, I'm having trouble processing your request. Please try again later.",
                "ru": "🚨 Извините, у меня возникли проблемы с обработкой вашего запроса. Пожалуйста, попробуйте позже."
            },
            
            "unexpected_error": {
                "en": "⚠️ An unexpected error occurred. Please try again later.",
                "ru": "⚠️ Произошла непредвиденная ошибка. Пожалуйста, попробуйте еще раз позже."
            },
            
            # Start command
            "start_greeting": {
                "en": "👋 Hi {user}\! I'm an AI assistant powered by OpenRouter\.\n\n"
                      "Just send me a message and I'll respond\. I remember our conversation context\! "
                      "Use /reset to clear our conversation history\.",
                "ru": "👋 Привет, {user}\! Я ИИ\-ассистент на платформе OpenRouter\.\n\n"
                      "Просто отправь мне сообщение и я отвечу\. Я запоминаю контекст нашего разговора\! "
                      "Используй /reset, чтобы очистить историю переписки\."
            },
            
            # Help command
            "help_text": {
                "en": "🤖 *Bot Help*\n\n"
                      "\- Just send me a message and I'll respond\n"
                      "\- I remember our conversation context\n"
                      "\- Use /reset to clear our conversation history\n"
                      "\- Use /model to see current AI model\n\n"
                      "⚠️ Please be patient if I'm slow \- I'm getting lots of messages\!",
                "ru": "🤖 *Помощь по боту*\n\n"
                      "\- Просто отправьте мне сообщение \\- и я отвечу\n"
                      "\- Я запоминаю контекст нашего разговора\n"
                      "\- Используйте /reset чтобы очистить историю беседы\n"
                      "\- Используйте /model чтобы узнать текущую модель ИИ\n\n"
                      "⚠️ Пожалуйста, проявляйте терпение, если я отвечаю медленно \- я получаю много сообщений\!"
            },
            
            # Reset context
            "context_cleared": {
                "en": "🔄 Conversation history cleared. Let's start fresh!",
                "ru": "🔄 История переписки очищена. Давайте начнем с чистого листа!"
            },
            
            "context_clear_failed": {
                "en": "⚠️ Failed to clear history. Please try again.",
                "ru": "⚠️ Не удалось очистить историю. Пожалуйста, попробуйте еще раз."
            },
            
            # Model info
            "current_model": {
                "en": "🤖 Currently using: {model_name}",
                "ru": "🤖 В настоящее время используется: {model_name}"
            }
        }
    
    def get_text(self, key: str, language: Language = None, **kwargs) -> str:
        """
        Get localized text by key.
        
        Args:
            key: The text key to retrieve
            language: Language to use (defaults to default_language)
            **kwargs: Format arguments for the text string
            
        Returns:
            Formatted localized text string
        """
        if language is None:
            language = self.default_language
        
        if key not in self._texts:
            return f"[Missing text: {key}]"
        
        lang_code = language.value
        if lang_code not in self._texts[key]:
            # Fallback to English if language not available
            lang_code = Language.ENGLISH.value
            if lang_code not in self._texts[key]:
                return f"[Missing translation: {key}]"
        
        text = self._texts[key][lang_code]
        
        # Format the text with provided arguments
        try:
            return text.format(**kwargs)
        except KeyError as e:
            return f"[Format error in {key}: missing {e}]"
    
    def set_default_language(self, language: Language):
        """Set the default language for the localization system"""
        self.default_language = language
    
    def get_available_languages(self) -> list[Language]:
        """Get list of available languages"""
        return list(Language)

    def add_text(self, key: str, translations: Dict[str, str]):
        """
        Add or update text translations.

        Args:
            key: The text key
            translations: Dictionary with language codes as keys and text as values
                         e.g., {"en": "Hello", "ru": "Привет"}
        """
        self._texts[key] = translations

    def get_all_keys(self) -> list[str]:
        """Get all available text keys"""
        return list(self._texts.keys())


# Global localization instance
localization = Localization()
